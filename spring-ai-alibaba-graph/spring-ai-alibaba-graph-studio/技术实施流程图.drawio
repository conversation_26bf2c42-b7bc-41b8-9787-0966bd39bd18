<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/28.0.6 Chrome/138.0.7204.100 Electron/37.2.3 Safari/537.36" version="28.0.6">
  <diagram name="技术实施流程图" id="technical-workflow">
    <mxGraphModel dx="1234" dy="808" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />

        <!-- 主标题 -->
        <mxCell id="main_title" value="🤖 智能自动化操作技术实施流程" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontColor=#2E3440;" vertex="1" parent="1">
          <mxGeometry x="350" y="30" width="460" height="40" as="geometry" />
        </mxCell>

        <!-- 装饰线 -->
        <mxCell id="deco_line" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#5E81AC;strokeColor=none;opacity=30;" vertex="1" parent="1">
          <mxGeometry x="400" y="75" width="360" height="3" as="geometry" />
        </mxCell>

        <mxCell id="start" value="🚀 开始" style="ellipse;whiteSpace=wrap;html=1;fillColor=#A3BE8C;strokeColor=#81A1C1;fontColor=#2E3440;fontSize=14;fontStyle=1;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="520" y="100" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 步骤数字标记 -->
        <mxCell id="step1_num" value="1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#5E81AC;strokeColor=#4C566A;fontColor=white;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="700" y="210" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 摄像头图标 -->
        <mxCell id="camera_icon" value="📷" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" vertex="1" parent="1">
          <mxGeometry x="440" y="210" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="screenshot" value="&lt;b&gt;📷 步骤1: 屏幕截图&lt;/b&gt;&lt;br/&gt;&lt;br/&gt;• 通过视觉模型捕获&lt;br/&gt;• 获取当前屏幕状态&lt;br/&gt;• 生成高质量图像数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5E81AC;strokeColor=#4C566A;fontColor=white;fontSize=12;align=left;spacingLeft=10;shadow=1;gradientColor=#3E5A7C;" parent="1" vertex="1">
          <mxGeometry x="480" y="200" width="200" height="100" as="geometry" />
        </mxCell>

        <mxCell id="step2_num" value="2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#B48EAD;strokeColor=#4C566A;fontColor=white;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="700" y="370" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- AI眼睛图标 -->
        <mxCell id="ai_icon" value="👁️" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" vertex="1" parent="1">
          <mxGeometry x="440" y="370" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="recognition" value="&lt;b&gt;👁️ 步骤2: 图像识别&lt;/b&gt;&lt;br/&gt;&lt;br/&gt;• AI视觉模型分析&lt;br/&gt;• 识别UI元素&lt;br/&gt;• 精确定位坐标&lt;br/&gt;• 元素分类标记" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#B48EAD;strokeColor=#4C566A;fontColor=white;fontSize=12;align=left;spacingLeft=10;shadow=1;gradientColor=#946A8D;" parent="1" vertex="1">
          <mxGeometry x="480" y="340" width="200" height="120" as="geometry" />
        </mxCell>

        <mxCell id="step3_num" value="3" style="ellipse;whiteSpace=wrap;html=1;fillColor=#D08770;strokeColor=#4C566A;fontColor=white;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="700" y="530" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 代码图标 -->
        <mxCell id="code_icon" value="💻" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" vertex="1" parent="1">
          <mxGeometry x="440" y="530" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="analysis" value="&lt;b&gt;💻 步骤3: 代码分析&lt;/b&gt;&lt;br/&gt;&lt;br/&gt;• 加载HTML结构&lt;br/&gt;• 解析JavaScript代码&lt;br/&gt;• 分析DOM元素&lt;br/&gt;• 理解页面逻辑" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#D08770;strokeColor=#4C566A;fontColor=white;fontSize=12;align=left;spacingLeft=10;shadow=1;gradientColor=#B06750;" parent="1" vertex="1">
          <mxGeometry x="480" y="500" width="200" height="120" as="geometry" />
        </mxCell>

        <mxCell id="step4_num" value="4" style="ellipse;whiteSpace=wrap;html=1;fillColor=#A3BE8C;strokeColor=#4C566A;fontColor=white;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="700" y="690" width="30" height="30" as="geometry" />
        </mxCell>

        <!-- 鼠标图标 -->
        <mxCell id="mouse_icon" value="🖱️" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=28;" vertex="1" parent="1">
          <mxGeometry x="440" y="690" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="execution" value="&lt;b&gt;🖱️ 步骤4: 操作执行&lt;/b&gt;&lt;br/&gt;&lt;br/&gt;• 模拟鼠标点击&lt;br/&gt;• 键盘输入操作&lt;br/&gt;• 滚动和拖拽&lt;br/&gt;• 人类行为模拟" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#A3BE8C;strokeColor=#4C566A;fontColor=white;fontSize=12;align=left;spacingLeft=10;shadow=1;gradientColor=#839E6C;" parent="1" vertex="1">
          <mxGeometry x="480" y="660" width="200" height="120" as="geometry" />
        </mxCell>

        <mxCell id="end" value="✅ 操作完成" style="ellipse;whiteSpace=wrap;html=1;fillColor=#BF616A;strokeColor=#81A1C1;fontColor=white;fontSize=14;fontStyle=1;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="520" y="820" width="120" height="60" as="geometry" />
        </mxCell>

        <!-- 进度条 -->
        <mxCell id="progress_bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E5E5E5;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="750" y="250" width="200" height="8" as="geometry" />
        </mxCell>

        <mxCell id="progress_fill" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#5E81AC;strokeColor=none;" vertex="1" parent="1">
          <mxGeometry x="750" y="250" width="50" height="8" as="geometry" />
        </mxCell>

        <mxCell id="progress_text" value="进度: 25%" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#2E3440;" vertex="1" parent="1">
          <mxGeometry x="750" y="265" width="80" height="20" as="geometry" />
        </mxCell>
        <!-- 左侧图标 -->
        <mxCell id="vision_icon" value="🧠" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;" vertex="1" parent="1">
          <mxGeometry x="160" y="190" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="details1" value="&lt;b&gt;🧠 视觉模型技术&lt;/b&gt;&lt;br/&gt;&lt;br/&gt;• 计算机视觉算法&lt;br/&gt;• 深度学习模型&lt;br/&gt;• 实时图像处理&lt;br/&gt;• 高精度截图技术" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ECEFF4;strokeColor=#D8DEE9;fontColor=#2E3440;fontSize=11;align=left;spacingLeft=10;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="200" y="180" width="180" height="140" as="geometry" />
        </mxCell>

        <mxCell id="ai_brain_icon" value="🤖" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;" vertex="1" parent="1">
          <mxGeometry x="160" y="360" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="details2" value="&lt;b&gt;🤖 AI识别能力&lt;/b&gt;&lt;br/&gt;&lt;br/&gt;• 按钮识别&lt;br/&gt;• 文本框定位&lt;br/&gt;• 菜单项检测&lt;br/&gt;• 图标分类&lt;br/&gt;• 链接识别" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ECEFF4;strokeColor=#D8DEE9;fontColor=#2E3440;fontSize=11;align=left;spacingLeft=10;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="200" y="330" width="180" height="140" as="geometry" />
        </mxCell>

        <!-- 右侧图标 -->
        <mxCell id="arch_icon" value="⚙️" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;" vertex="1" parent="1">
          <mxGeometry x="970" y="520" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="tech1" value="&lt;b&gt;⚙️ 技术架构&lt;/b&gt;&lt;br/&gt;&lt;br/&gt;• 前端渲染引擎&lt;br/&gt;• JavaScript解释器&lt;br/&gt;• DOM树分析&lt;br/&gt;• 事件监听机制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ECEFF4;strokeColor=#D8DEE9;fontColor=#2E3440;fontSize=11;align=left;spacingLeft=10;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="780" y="490" width="180" height="140" as="geometry" />
        </mxCell>

        <mxCell id="engine_icon" value="🔧" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;" vertex="1" parent="1">
          <mxGeometry x="970" y="680" width="30" height="30" as="geometry" />
        </mxCell>

        <mxCell id="tech2" value="&lt;b&gt;🔧 执行引擎&lt;/b&gt;&lt;br/&gt;&lt;br/&gt;• 操作系统API&lt;br/&gt;• 硬件接口调用&lt;br/&gt;• 时序控制&lt;br/&gt;• 异常处理机制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ECEFF4;strokeColor=#D8DEE9;fontColor=#2E3440;fontSize=11;align=left;spacingLeft=10;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="780" y="650" width="180" height="140" as="geometry" />
        </mxCell>
        <!-- 背景装饰圆圈 -->
        <mxCell id="bg_circle1" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#E8F4FD;strokeColor=none;opacity=20;" vertex="1" parent="1">
          <mxGeometry x="450" y="170" width="260" height="160" as="geometry" />
        </mxCell>

        <mxCell id="bg_circle2" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F5E6F3;strokeColor=none;opacity=20;" vertex="1" parent="1">
          <mxGeometry x="450" y="310" width="260" height="180" as="geometry" />
        </mxCell>

        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#5E81AC;strokeWidth=3;endArrow=classic;endFill=1;flowAnimation=1;" parent="1" source="start" target="screenshot" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#B48EAD;strokeWidth=3;endArrow=classic;endFill=1;flowAnimation=1;" parent="1" source="screenshot" target="recognition" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#D08770;strokeWidth=3;endArrow=classic;endFill=1;flowAnimation=1;" parent="1" source="recognition" target="analysis" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#A3BE8C;strokeWidth=3;endArrow=classic;endFill=1;flowAnimation=1;" parent="1" source="analysis" target="execution" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#BF616A;strokeWidth=3;endArrow=classic;endFill=1;flowAnimation=1;" parent="1" source="execution" target="end" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="detail_arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#D8DEE9;strokeWidth=2;endArrow=none;endFill=0;dashed=1;flowAnimation=1;" parent="1" source="details1" target="screenshot" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="detail_arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#D8DEE9;strokeWidth=2;endArrow=none;endFill=0;dashed=1;flowAnimation=1;" parent="1" source="details2" target="recognition" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tech_arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#D8DEE9;strokeWidth=2;endArrow=none;endFill=0;dashed=1;flowAnimation=1;" parent="1" source="tech1" target="analysis" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="tech_arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#D8DEE9;strokeWidth=2;endArrow=none;endFill=0;dashed=1;flowAnimation=1;" parent="1" source="tech2" target="execution" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
