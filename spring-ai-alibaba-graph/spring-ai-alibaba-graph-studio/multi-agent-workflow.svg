<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 24px; font-weight: bold; text-anchor: middle; fill: #333; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; fill: #666; }
      .box-text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; fill: #333; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; fill: #555; }
      .stage-box { fill: #E3F2FD; stroke: #1976D2; stroke-width: 2; rx: 10; }
      .user-box { fill: #FFCDD2; stroke: #D32F2F; stroke-width: 2; }
      .agent-box { fill: #C8E6C9; stroke: #388E3C; stroke-width: 2; rx: 5; }
      .code-agent-box { fill: #FFF3E0; stroke: #F57C00; stroke-width: 2; rx: 5; }
      .data-agent-box { fill: #E1F5FE; stroke: #0288D1; stroke-width: 2; rx: 5; }
      .result-box { fill: #FCE4EC; stroke: #E91E63; stroke-width: 2; stroke-dasharray: 5,5; rx: 5; }
      .optimization-box { fill: #FFF8E1; stroke: #FBC02D; stroke-width: 2; rx: 10; }
      .integration-box { fill: #E8F5E8; stroke: #4CAF50; stroke-width: 2; rx: 10; }
      .tech-box { fill: #F3E5F5; stroke: #9C27B0; stroke-width: 2; rx: 10; }
      .arrow { stroke: #1976D2; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .feedback-arrow { stroke: #D32F2F; stroke-width: 3; fill: none; marker-end: url(#arrowhead-red); stroke-dasharray: 8,4; }
    </style>
    <marker id="arrowhead" markerWidth="12" markerHeight="10" refX="11" refY="5" orient="auto" markerUnits="strokeWidth">
      <polygon points="0 0, 12 5, 0 10" fill="#1976D2" />
    </marker>
    <marker id="arrowhead-red" markerWidth="12" markerHeight="10" refX="11" refY="5" orient="auto" markerUnits="strokeWidth">
      <polygon points="0 0, 12 5, 0 10" fill="#D32F2F" />
    </marker>
  </defs>
  
  <!-- Title -->
  <text x="500" y="30" class="title">以 Manus 为例：Multi-Agent 智能体系统工作流程</text>
  
  <!-- User Input -->
  <polygon points="50,80 100,100 50,120 20,100" class="user-box"/>
  <text x="50" y="105" class="box-text">用户</text>
  
  <!-- Stage 1: Intent Recognition -->
  <rect x="120" y="60" width="180" height="80" class="stage-box"/>
  <text x="210" y="80" class="box-text" font-weight="bold">1. 意图识别阶段</text>
  <text x="210" y="95" class="small-text">- 意图识别和关键词提取</text>
  <text x="210" y="107" class="small-text">- 交互式引导</text>
  <text x="210" y="119" class="small-text">- 多模态输入支持</text>
  
  <!-- Stage 2: Task Initialization -->
  <rect x="350" y="60" width="180" height="80" class="stage-box"/>
  <text x="440" y="80" class="box-text" font-weight="bold">2. 任务初始化</text>
  <text x="440" y="95" class="small-text">创建任务目录</text>
  <text x="440" y="107" class="small-text">启动隔离容器</text>
  <text x="440" y="119" class="small-text" font-style="italic">确保执行隔离性和安全性</text>
  
  <!-- Stage 3: Task Planning -->
  <rect x="580" y="60" width="180" height="80" class="stage-box"/>
  <text x="670" y="80" class="box-text" font-weight="bold">3. 任务规划</text>
  <text x="670" y="95" class="small-text">复杂任务分解</text>
  <text x="670" y="107" class="small-text">子任务定义</text>
  <text x="670" y="119" class="small-text" font-style="italic">生成结构化任务列表</text>
  
  <!-- Stage 4: Task Execution (Large Box) -->
  <rect x="200" y="200" width="500" height="200" class="stage-box"/>
  <text x="450" y="220" class="box-text" font-weight="bold" font-size="16px">4. 任务执行</text>
  
  <!-- Search Agent -->
  <rect x="230" y="250" width="120" height="80" class="agent-box"/>
  <text x="290" y="270" class="box-text" font-weight="bold">Search Agent</text>
  <text x="290" y="285" class="small-text">网络信息搜索</text>
  <text x="290" y="297" class="small-text">内容验证与保存</text>
  
  <!-- Code Agent -->
  <rect x="370" y="250" width="120" height="80" class="code-agent-box"/>
  <text x="430" y="270" class="box-text" font-weight="bold">Code Agent</text>
  <text x="430" y="285" class="small-text">代码生成</text>
  <text x="430" y="297" class="small-text">代码执行</text>
  
  <!-- Data Analysis Agent -->
  <rect x="510" y="250" width="120" height="80" class="data-agent-box"/>
  <text x="570" y="270" class="box-text" font-weight="bold">Data Analysis</text>
  <text x="570" y="282" class="box-text" font-weight="bold">Agent</text>
  <text x="570" y="297" class="small-text">数据分析与处理</text>
  
  <!-- Task Result -->
  <rect x="350" y="350" width="200" height="30" class="result-box"/>
  <text x="450" y="370" class="box-text" font-weight="bold">任务目标：存储执行结果</text>
  
  <!-- Stage 5: Result Integration -->
  <rect x="350" y="450" width="180" height="80" class="integration-box"/>
  <text x="440" y="470" class="box-text" font-weight="bold">5. 结果整合</text>
  <text x="440" y="485" class="small-text">- 整合执行结果</text>
  <text x="440" y="497" class="small-text">- 生成用户友好输出</text>
  <text x="440" y="509" class="small-text">- 收集反馈进行优化</text>
  
  <!-- Optimization Direction -->
  <rect x="50" y="550" width="200" height="100" class="optimization-box"/>
  <text x="150" y="570" class="box-text" font-weight="bold">优化方向</text>
  <text x="150" y="585" class="small-text">1. 升级为DAG结构</text>
  <text x="150" y="597" class="small-text">2. 自动化测试与质量控制</text>
  <text x="150" y="609" class="small-text">3. 人机混合交互模式</text>
  
  <!-- Technical Architecture -->
  <rect x="700" y="550" width="200" height="100" class="tech-box"/>
  <text x="800" y="570" class="box-text" font-weight="bold">技术架构依赖</text>
  <text x="800" y="585" class="small-text">小型模型：意图识别</text>
  <text x="800" y="597" class="small-text">Deepseek-r1：任务规划</text>
  <text x="800" y="609" class="small-text">Claude-3.7：多模态任务</text>
  
  <!-- Arrows -->
  <!-- Main flow arrows -->
  <path d="M 100 100 L 115 100" class="arrow"/>
  <path d="M 300 100 L 345 100" class="arrow"/>
  <path d="M 530 100 L 575 100" class="arrow"/>
  <path d="M 670 140 L 670 175" class="arrow"/>
  <path d="M 670 175 L 455 175" class="arrow"/>
  <path d="M 455 175 L 455 195" class="arrow"/>

  <!-- Agent to result arrows -->
  <path d="M 290 330 L 290 345 L 375 345" class="arrow"/>
  <path d="M 430 330 L 430 345" class="arrow"/>
  <path d="M 570 330 L 570 345 L 525 345" class="arrow"/>

  <!-- Result to integration -->
  <path d="M 450 380 L 450 445" class="arrow"/>

  <!-- Feedback arrows with better curves -->
  <path d="M 50 450 Q 20 350 20 250 Q 20 150 45 120" class="feedback-arrow"/>
  <path d="M 800 550 Q 830 450 830 350 Q 830 250 805 220" class="feedback-arrow"/>
  <path d="M 250 550 Q 120 500 120 450 Q 120 400 245 380" class="feedback-arrow"/>
  
  <!-- Bottom summary box -->
  <rect x="50" y="720" width="900" height="40" fill="#F5F5F5" stroke="#999" stroke-width="1" rx="5"/>
  <text x="500" y="745" class="subtitle">Multi-Agent 系统特点：环境隔离的任务执行 | 模块化的 Agent 设计 | 灵活的任务调度机制</text>
</svg>
