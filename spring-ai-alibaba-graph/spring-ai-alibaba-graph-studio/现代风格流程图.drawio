<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-02T00:00:00.000Z" agent="draw.io" version="22.1.16" type="device">
  <diagram name="智能自动化操作流程" id="automation-workflow">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="800" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 主容器背景 -->
        <mxCell id="main_container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F8F9FA;strokeColor=#6C7B7F;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="1120" height="720" as="geometry" />
        </mxCell>
        
        <!-- 左侧标题区域 -->
        <mxCell id="title_area" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#90CAF9;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="60" y="80" width="200" height="640" as="geometry" />
        </mxCell>
        
        <!-- 主标题 -->
        <mxCell id="main_title" value="ChatGPT Agent&lt;br&gt;统一代理系统" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=24;fontStyle=1;fontColor=#1565C0;" vertex="1" parent="1">
          <mxGeometry x="80" y="180" width="160" height="80" as="geometry" />
        </mxCell>

        <!-- 副标题 -->
        <mxCell id="sub_title" value="Unified Agentic System" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=2;fontColor=#757575;" vertex="1" parent="1">
          <mxGeometry x="80" y="260" width="160" height="30" as="geometry" />
        </mxCell>
        
        <!-- 用户图标 -->
        <mxCell id="user" value="👨‍💻&lt;br&gt;用户" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontColor=#424242;" vertex="1" parent="1">
          <mxGeometry x="320" y="120" width="60" height="60" as="geometry" />
        </mxCell>
        
        <!-- 查询框 -->
        <mxCell id="query_box" value="❓&lt;br&gt;Query" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;fontSize=12;fontColor=#2E7D32;" vertex="1" parent="1">
          <mxGeometry x="420" y="120" width="80" height="60" as="geometry" />
        </mxCell>
        
        <!-- 步骤1箭头 -->
        <mxCell id="arrow1" value="1&lt;br&gt;Input" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#9E9E9E;strokeWidth=2;endArrow=classic;endFill=1;dashed=1;fontSize=10;fontColor=#757575;" edge="1" parent="1" source="query_box" target="llm1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="580" y="150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- ChatGPT Agent 核心 -->
        <mxCell id="llm1" value="🤖&lt;br&gt;ChatGPT&lt;br&gt;Agent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#2196F3;strokeWidth=2;fontSize=12;fontColor=#1565C0;" vertex="1" parent="1">
          <mxGeometry x="580" y="120" width="100" height="60" as="geometry" />
        </mxCell>

        <!-- 虚拟电脑沙盒 -->
        <mxCell id="function_def" value="💻&lt;br&gt;Virtual&lt;br&gt;Sandbox" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#9C27B0;strokeWidth=2;fontSize=12;fontColor=#7B1FA2;" vertex="1" parent="1">
          <mxGeometry x="900" y="120" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 步骤1反向箭头 -->
        <mxCell id="arrow1_back" value="1&lt;br&gt;Input" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#9E9E9E;strokeWidth=2;endArrow=classic;endFill=1;dashed=1;fontSize=10;fontColor=#757575;" edge="1" parent="1" source="function_def" target="llm1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 任务分析 -->
        <mxCell id="prepare_call" value="2&lt;br&gt;Task&lt;br&gt;Analysis" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4CAF50;strokeWidth=2;endArrow=classic;endFill=1;fontSize=10;fontColor=#2E7D32;" edge="1" parent="1" source="llm1" target="function_call">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="630" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 工具选择器 -->
        <mxCell id="function_call" value="🎯&lt;br&gt;Tool&lt;br&gt;Selector" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#FF9800;strokeWidth=2;fontSize=12;fontColor=#E65100;" vertex="1" parent="1">
          <mxGeometry x="580" y="250" width="100" height="60" as="geometry" />
        </mxCell>

        <!-- 自动选择工具 -->
        <mxCell id="invoke_tool" value="3&lt;br&gt;Auto Select" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF5722;strokeWidth=2;endArrow=classic;endFill=1;fontSize=10;fontColor=#D84315;" edge="1" parent="1" source="function_call" target="tools_apis">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="850" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>

        <!-- 四种智能工具 -->
        <mxCell id="tools_apis" value="🛠️&lt;br&gt;Smart&lt;br&gt;Tools" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFEBEE;strokeColor=#F44336;strokeWidth=2;fontSize=12;fontColor=#C62828;" vertex="1" parent="1">
          <mxGeometry x="900" y="250" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 响应 -->
        <mxCell id="response_arrow" value="4&lt;br&gt;Response" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#607D8B;strokeWidth=2;endArrow=classic;endFill=1;fontSize=10;fontColor=#37474F;" edge="1" parent="1" source="tools_apis" target="tool_output">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="950" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 工具输出 -->
        <mxCell id="tool_output" value="📄&lt;br&gt;Tool&lt;br&gt;Output" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E0F2F1;strokeColor=#009688;strokeWidth=2;fontSize=12;fontColor=#00695C;" vertex="1" parent="1">
          <mxGeometry x="900" y="380" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 信息整合 -->
        <mxCell id="prompt_arrow" value="5&lt;br&gt;Information&lt;br&gt;Integration" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#795548;strokeWidth=2;endArrow=classic;endFill=1;fontSize=10;fontColor=#5D4037;" edge="1" parent="1" source="tool_output" target="llm2">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="680" y="410" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- 推理与分析 (Deep Research) -->
        <mxCell id="llm2" value="🧠&lt;br&gt;Deep&lt;br&gt;Research" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#2196F3;strokeWidth=2;fontSize=12;fontColor=#1565C0;" vertex="1" parent="1">
          <mxGeometry x="580" y="380" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 生成响应 -->
        <mxCell id="generate_arrow" value="6&lt;br&gt;Generate" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#8BC34A;strokeWidth=2;endArrow=classic;endFill=1;fontSize=10;fontColor=#689F38;" edge="1" parent="1" source="llm2" target="llm_response">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="420" y="410" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- LLM 响应 -->
        <mxCell id="llm_response" value="📝&lt;br&gt;LLM&lt;br&gt;response" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F1F8E9;strokeColor=#8BC34A;strokeWidth=2;fontSize=12;fontColor=#689F38;" vertex="1" parent="1">
          <mxGeometry x="320" y="380" width="100" height="60" as="geometry" />
        </mxCell>
        
        <!-- 视觉化浏览器 -->
        <mxCell id="visual_browser" value="🌐 Visual Browser&lt;br&gt;&lt;br&gt;• GUI网页操作&lt;br&gt;• 点击表单填写&lt;br&gt;• 模拟用户浏览" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E8F5E8;strokeColor=#4CAF50;strokeWidth=2;fontSize=11;fontColor=#2E7D32;align=left;spacingLeft=10;" vertex="1" parent="1">
          <mxGeometry x="320" y="520" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- 文字浏览器 -->
        <mxCell id="text_browser" value="📝 Text Browser&lt;br&gt;&lt;br&gt;• 大量文字处理&lt;br&gt;• 逻辑推理&lt;br&gt;• 内容抽取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#E3F2FD;strokeColor=#2196F3;strokeWidth=2;fontSize=11;fontColor=#1565C0;align=left;spacingLeft=10;" vertex="1" parent="1">
          <mxGeometry x="500" y="520" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- 终端机 -->
        <mxCell id="terminal_tool" value="⌨️ Terminal&lt;br&gt;&lt;br&gt;• 执行程序代码&lt;br&gt;• CLI工具使用&lt;br&gt;• 文件下载分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF3E0;strokeColor=#FF9800;strokeWidth=2;fontSize=11;fontColor=#E65100;align=left;spacingLeft=10;" vertex="1" parent="1">
          <mxGeometry x="680" y="520" width="140" height="80" as="geometry" />
        </mxCell>

        <!-- API存取 -->
        <mxCell id="api_access" value="🔗 API Access&lt;br&gt;&lt;br&gt;• Gmail连接&lt;br&gt;• Google Drive&lt;br&gt;• GitHub集成" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFEBEE;strokeColor=#F44336;strokeWidth=2;fontSize=11;fontColor=#C62828;align=left;spacingLeft=10;" vertex="1" parent="1">
          <mxGeometry x="860" y="520" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- 工具连接箭头 -->
        <mxCell id="step_arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#4CAF50;strokeWidth=2;endArrow=classic;endFill=1;" edge="1" parent="1" source="visual_browser" target="text_browser">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="step_arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#2196F3;strokeWidth=2;endArrow=classic;endFill=1;" edge="1" parent="1" source="text_browser" target="terminal_tool">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="step_arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF9800;strokeWidth=2;endArrow=classic;endFill=1;" edge="1" parent="1" source="terminal_tool" target="api_access">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- 数字标记 -->
        <mxCell id="num1" value="1" style="ellipse;whiteSpace=wrap;html=1;fillColor=#4CAF50;strokeColor=none;fontColor=white;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="375" y="490" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="num2" value="2" style="ellipse;whiteSpace=wrap;html=1;fillColor=#2196F3;strokeColor=none;fontColor=white;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="555" y="490" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="num3" value="3" style="ellipse;whiteSpace=wrap;html=1;fillColor=#FF9800;strokeColor=none;fontColor=white;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="735" y="490" width="30" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="num4" value="4" style="ellipse;whiteSpace=wrap;html=1;fillColor=#F44336;strokeColor=none;fontColor=white;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="915" y="490" width="30" height="30" as="geometry" />
        </mxCell>
        
        <!-- 系统整合能力说明 -->
        <mxCell id="integration_note" value="🔄 系统整合能力&lt;br&gt;Operator + Deep Research" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFF8E1;strokeColor=#FFC107;strokeWidth=1;fontSize=10;fontColor=#F57F17;align=center;" vertex="1" parent="1">
          <mxGeometry x="80" y="320" width="160" height="50" as="geometry" />
        </mxCell>

        <!-- 虚拟电脑特性 -->
        <mxCell id="sandbox_note" value="🔒 虚拟电脑沙盒&lt;br&gt;• 沙盒隔离&lt;br&gt;• 上下文持续性&lt;br&gt;• 多步流程追踪" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#F3E5F5;strokeColor=#9C27B0;strokeWidth=1;fontSize=9;fontColor=#7B1FA2;align=left;spacingLeft=5;" vertex="1" parent="1">
          <mxGeometry x="1020" y="100" width="120" height="80" as="geometry" />
        </mxCell>

        <!-- 多模态操作引擎 -->
        <mxCell id="multimodal_note" value="🎯 多模态操作引擎&lt;br&gt;自动选择最适工具" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#FFEBEE;strokeColor=#F44336;strokeWidth=1;fontSize=10;fontColor=#C62828;align=center;" vertex="1" parent="1">
          <mxGeometry x="1020" y="230" width="120" height="50" as="geometry" />
        </mxCell>

        <!-- 底部说明 -->
        <mxCell id="footer_text" value="🤖 ChatGPT Agent 统一代理系统 - 核心技术架构" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#757575;fontStyle=2;" vertex="1" parent="1">
          <mxGeometry x="400" y="650" width="400" height="30" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
