在构建AI应用时，开发者经常面临一个共同的挑战：如何高效管理和动态更新提示词(Prompt)模板。

特别是在生产环境中，我们希望能够在不重启应用的情况下调整AI模型的提示词，以快速响应业务需求变化或优化AI输出质量。Spring AI Alibaba结合Nacos配置中心提供了一个优雅的解决方案，本文将详细介绍如何利用这一组合实现动态Prompt管理。

## 动态Prompt管理的挑战

在传统的Spring AI应用中，Prompt模板通常以以下几种方式管理：

1. **Hard code**
   - 优点：实现简单，直接
   - 缺点：每次修改都需要重新编译和部署应用，无法动态调整
   - 缺点：不同场景的Prompt混杂在代码中，难以统一管理和维护
   - 缺点：开发人员和AI提示工程师需要紧密协作，流程繁琐

2. **存储在配置文件中**
   - 优点：将Prompt与代码分离，便于管理
   - 缺点：配置文件通常随应用一起打包，修改后仍需重启应用才能生效
   - 缺点：无法实现实时动态更新

3. **保存在数据库中**
   - 优点：可以实现动态更新
   - 缺点：需要额外的数据访问层和刷新机制
   - 缺点：需要开发专门的管理界面
   - 缺点：数据库操作可能引入额外的性能开销和复杂性

这些方法各有优缺点，但都存在一定的局限性，难以满足现代AI应用对Prompt模板灵活管理的需求。

## Spring AI Alibaba与Nacos的完美结合

Spring AI Alibaba通过与Nacos的集成，提供了一种更为优雅的解决方案。Nacos作为一个动态服务发现和配置管理平台，能够实现配置的集中管理和动态推送，非常适合管理AI应用中的Prompt模板。

### 核心优势

1. **动态更新**：修改Nacos中的配置后，应用可以实时感知并更新，无需重启
2. **集中管理**：所有Prompt模板可在Nacos控制台统一管理
3. **版本控制**：Nacos提供配置版本历史，可以轻松回滚到之前的Prompt版本
4. **环境隔离**：通过Nacos的命名空间功能，可以为不同环境(开发、测试、生产)维护不同的Prompt配置

## 实现步骤

### 1. 添加依赖

首先，在项目的`pom.xml`中添加相关依赖：

```xml
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    <version>${latest.version}</version>
</dependency>

<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>spring-ai-alibaba-core</artifactId>
    <version>${latest.version}</version>
</dependency>
```

### 2. 配置Nacos连接

在`application.properties`或`application.yml`中配置Nacos服务连接：

```properties
# Nacos服务地址
spring.cloud.nacos.config.server-addr=${NACOS_SERVER_ADDR:localhost:8848}

# Nacos命名空间
spring.cloud.nacos.config.namespace=${NACOS_NAMESPACE:public}

# 认证信息（如果需要）
spring.cloud.nacos.config.access-key=${NACOS_ACCESS_KEY:}
spring.cloud.nacos.config.secret-key=${NACOS_SECRET_KEY:}

# 启用配置自动刷新
spring.cloud.nacos.config.refresh-enabled=true
```

### 3. 创建Prompt模板配置

在Nacos控制台创建配置项，Data ID为`spring.ai.alibaba.configurable.prompt`，Group为`DEFAULT_GROUP`，配置格式为JSON：

```json
[
  {
    "name": "book-recommendation",
    "template": "请推荐三本{author}的著名著作，简要介绍每本书的核心内容和影响力。回答要简洁，每本书不超过30字。",
    "model": {
      "temperature": 0.7,
      "topP": 0.9
    }
  },
  {
    "name": "code-review",
    "template": "你是一位资深的代码审查专家，请对以下{language}代码进行审查并提出改进建议：\n\n```{language}\n{code}\n```",
    "model": {
      "temperature": 0.2,
      "topP": 0.95
    }
  },
  {
    "name": "author",
    "template": "列出 {author} 有名的著作",
    "model": {
      "key": "余华"
    }
  }
]
```

### 配置导入方式说明

Spring AI Alibaba提供了两种方式来监听和导入Nacos中的Prompt配置：

#### 方式一：使用默认的Data ID（推荐）

Spring AI Alibaba内置了对`spring.ai.alibaba.configurable.prompt`这个Data ID的支持。当使用这个默认Data ID时，框架会自动通过`@NacosConfigListener`注解监听配置变更，并更新Prompt模板。这是最简单的使用方式，不需要额外配置。

```java
@NacosConfigListener(
    dataId = "spring.ai.alibaba.configurable.prompt",
    group = "DEFAULT_GROUP",
    initNotify = true
)
protected void onConfigChange(List<ConfigurablePromptTemplateModel> configList) {
    // 这个方法在Spring AI Alibaba内部已实现，会自动处理配置更新
}
```

#### 方式二：使用自定义Data ID

如果您希望使用自定义的Data ID（例如`prompt-config.json`），则需要在`application.yml`或`application.properties`中添加以下配置：

```yaml
spring:
  config:
    import:
      - "optional:nacos:prompt-config.json"
```

或者在properties格式中：

```properties
spring.config.import=optional:nacos:prompt-config.json
```

这种方式允许您使用自定义的配置文件名，更加灵活。但需要注意，使用自定义Data ID时，您需要确保配置的格式与Spring AI Alibaba期望的格式一致，并且可能需要自行实现配置监听和更新逻辑。

#### 两种方式的选择建议

- 如果您是初次使用或希望快速集成，建议使用方式一（默认Data ID）
- 如果您有特殊的命名需求或已有成熟的配置管理体系，可以使用方式二（自定义Data ID）

无论使用哪种方式，都能实现Prompt模板的动态更新，关键区别在于配置复杂度和灵活性。

### 4. 在控制器中使用动态Prompt

```java
@RestController
@RequestMapping("/prompt")
public class PromptController {

    private final ChatClient chatClient;
    private final ConfigurablePromptTemplateFactory promptTemplateFactory;
    
    public PromptController(ChatClient.Builder builder, 
                        ConfigurablePromptTemplateFactory promptTemplateFactory) {
        this.chatClient = builder.build();
        this.promptTemplateFactory = promptTemplateFactory;
    }
    
    @GetMapping("/author-works")
    public AssistantMessage listAuthorWorks(@RequestParam String author) {
        // 获取名为"author"的模板
        ConfigurablePromptTemplate template = promptTemplateFactory.getTemplate("author");
        if (template == null) {
            // 如果模板不存在，创建一个默认模板
            template = promptTemplateFactory.create("author", "列出 {author} 有名的著作");
        }
        
        // 填充模板参数
        Prompt prompt = template.create(Map.of("author", author));
        // 执行AI调用
        return chatClient.prompt(prompt).call().chatResponse().getResult().getOutput();
    }
    
    @PostMapping("/execute/{templateName}")
    public AssistantMessage executePrompt(
            @PathVariable String templateName,
            @RequestBody(required = false) Map<String, Object> params) {
        
        if (params == null) {
            params = new HashMap<>();
        }
        
        ConfigurablePromptTemplate template = promptTemplateFactory.getTemplate(templateName);
        if (template == null) {
            throw new IllegalArgumentException("Template not found: " + templateName);
        }
        
        Prompt prompt = template.create(params);
        return chatClient.prompt(prompt).call().chatResponse().getResult().getOutput();
    }
}
```

## 实际应用场景

### 1. Prompt模板动态调整与优化

在AI应用开发过程中，Prompt模板的质量直接影响模型输出的效果。通过Nacos配置中心，AI工程师可以在不重启应用的情况下，持续优化Prompt模板：

- **A/B测试**：同时部署多个版本的Prompt模板，通过实际效果比较选择最优方案
- **快速迭代**：根据用户反馈，实时调整Prompt模板，提升用户体验
- **专业分工**：开发人员负责应用逻辑，AI提示工程师专注于优化Prompt，各司其职

例如，当我们发现"author"模板生成的回答过于简略时，可以在Nacos控制台立即调整模板：

```json
[
  {
    "name": "author",
    "template": "请详细介绍{author}的主要著作（至少5部），包括出版年份、核心主题和文学价值。",
    "model": {
      "temperature": 0.6,
      "topP": 0.9
    }
  }
]
```

修改后，应用会自动感知配置变化并刷新Prompt模板，下一次API调用将使用新的模板生成更详细的回答。

### 2. 算法模型参数热更新

Spring AI Alibaba还支持通过Nacos动态调整模型调用参数。在Nacos控制台创建配置项，Data ID为`spring.ai.alibaba.dashscope.chat.options`，可以调整temperature、topP等参数，实现模型行为的动态调整：

```json
{
  "temperature": 0.7,
  "topP": 0.9,
  "maxTokens": 2000
}
```

这对于需要根据不同业务场景调整模型行为的应用非常有用。例如，在创意写作场景中可以提高temperature值增加随机性，而在代码生成场景中则可以降低temperature值提高确定性。

### 3. 多环境配置管理

在企业级应用中，通常需要维护多个环境（开发、测试、预发、生产）的配置。Nacos的命名空间功能使得不同环境的Prompt模板可以完全隔离：

- 开发环境：使用更激进的模型参数，便于快速测试新功能
- 测试环境：模拟生产环境配置，进行全面测试
- 生产环境：使用经过充分验证的稳定配置

通过简单切换命名空间，应用可以无缝适配不同环境的配置要求。

## 高级功能：敏感配置加密存储

对于API密钥等敏感信息，Nacos结合KMS提供了配置加密存储能力。在`application.properties`中添加：

```properties
# 导入加密配置
spring.config.import=optional:nacos:cipher-kms-aes-256-encrypted-config.properties?group=DEFAULT_GROUP&refreshEnabled=true

# KMS区域配置
spring.cloud.nacos.config.kms_region_id=cn-hangzhou
spring.cloud.nacos.config.kmsVersion=v1.0

# 使用占位符引用加密配置
spring.ai.dashscope.api-key=${AI_DASHSCOPE_API_KEY}
```

在Nacos控制台创建加密配置项，Data ID为`cipher-kms-aes-256-encrypted-config.properties`，内容为：

```properties
AI_DASHSCOPE_API_KEY=your-encrypted-api-key
```

这样，敏感的API密钥等信息就可以安全地存储在配置中心，而不必担心明文泄露的风险。

## 最佳实践

1. **分类管理**：根据不同的业务场景或AI功能，在Nacos中创建不同的配置项
2. **环境隔离**：利用Nacos的命名空间功能，为开发、测试和生产环境维护独立的配置
3. **版本控制**：重要的Prompt调整前，先在Nacos中备份当前配置
4. **监控与告警**：配置Nacos的监控告警功能，及时发现配置问题
5. **权限管理**：合理设置Nacos的访问权限，避免未授权的配置修改
6. **配置审计**：利用Nacos的配置历史功能，记录所有配置变更，便于追踪问题

## 总结
https://github.com/alibaba/spring-ai-alibaba

Spring AI Alibaba结合Nacos提供了一种优雅的动态Prompt管理解决方案，使开发者能够在不重启应用的情况下调整AI模型的提示词模板。这种方式不仅提高了开发效率，也为AI应用的运维和优化提供了极大的灵活性。

通过集中管理Prompt模板，团队可以更好地协作开发AI功能，快速响应业务需求变化，持续优化AI输出质量。同时，Nacos的配置版本管理和环境隔离功能，也为Prompt模板的迭代和测试提供了有力支持。

在实际应用中，我们可以根据业务需求进一步扩展这一方案，例如添加Prompt模板的权限控制、使用情况统计、效果评估等功能，打造一个完整的Prompt工程平台。 